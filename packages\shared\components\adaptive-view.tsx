import * as React from "react";
import { cn } from "../lib/cn";
import { Slot } from "@radix-ui/react-slot";

type AdaptiveViewContextValue = {
  activeView: string;
  setActiveView: React.Dispatch<React.SetStateAction<string>>;
};

const AdaptiveViewContext = React.createContext<
  AdaptiveViewContextValue | undefined
>(undefined);

function useAdaptiveViewContext() {
  const context = React.useContext(AdaptiveViewContext);
  if (!context) {
    throw new Error(
      "AdaptiveView components must be used within a AdaptiveView.Root"
    );
  }
  return context;
}

interface AdaptiveViewRootProps extends React.HTMLAttributes<HTMLDivElement> {
  defaultView?: string;
}

function Root({
  children,
  defaultView = "",
  className,
  ...props
}: AdaptiveViewRootProps) {
  const [activeView, setActiveView] = React.useState<string>(defaultView);

  return (
    <AdaptiveViewContext.Provider value={{ activeView, setActiveView }}>
      <div className={cn("w-full", className)} {...props}>
        {children}
      </div>
    </AdaptiveViewContext.Provider>
  );
}
Root.displayName = "AdaptiveView.Root";

interface AdaptiveViewTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  view: string;
  asChild?: boolean;
}

function Trigger({
  view,
  className,
  children,
  asChild = false,
  ...props
}: AdaptiveViewTriggerProps) {
  const { activeView, setActiveView } = useAdaptiveViewContext();
  const isActive = activeView === view;
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      className={cn(
        "md:hidden", // Only show on mobile (<768px)
        className
      )}
      onClick={() => !isActive && setActiveView(view)}
      data-state={isActive ? "active" : "inactive"}
      disabled={isActive}
      {...props}
    >
      {children}
    </Comp>
  );
}
Trigger.displayName = "AdaptiveView.Trigger";

type AdaptiveViewTriggerGroupProps = React.HTMLAttributes<HTMLDivElement>;

function TriggerGroup({
  className,
  children,
  ...props
}: AdaptiveViewTriggerGroupProps) {
  return (
    <div
      className={cn(
        "md:hidden", // Only show on mobile (<768px)
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}
TriggerGroup.displayName = "AdaptiveView.TriggerGroup";

interface AdaptiveViewProps extends React.HTMLAttributes<HTMLDivElement> {
  view: string;
}

function View({ view, className, children, ...props }: AdaptiveViewProps) {
  const { activeView } = useAdaptiveViewContext();
  const isActive = activeView === view;

  return (
    <div
      className={cn(
        // Hide on mobile when not active, always show on md and up
        isActive ? "block" : "hidden md:block",
        className
      )}
      data-state={isActive ? "active" : "inactive"}
      {...props}
    >
      {children}
    </div>
  );
}
View.displayName = "AdaptiveView.View";

const AdaptiveView = {
  Root,
  Trigger,
  TriggerGroup,
  View
};

export { AdaptiveView };
