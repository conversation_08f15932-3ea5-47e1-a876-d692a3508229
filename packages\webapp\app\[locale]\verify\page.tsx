import { z } from "zod";
import { redirect } from "shared/lib/i18n/navigation";
import { Card, CardContent } from "shared/components/ui/card";
import { BackButton } from "@/app/components/BackButton";
import { OTPVerificationForm } from "./components/OTPVerificationForm";
import { getLocale } from "shared/lib";
import { redirectAuthenticated } from "@/lib";

interface VerifyPageProps {
  searchParams: Promise<{ email?: string }>;
}

export default async function VerifyPage({ searchParams }: VerifyPageProps) {
  const email = (await searchParams).email;
  const locale = await getLocale();

  await redirectAuthenticated("/");

  // Validate email
  if (!email) {
    redirect({ href: { pathname: "/login" }, locale });
  }

  // Validate email format
  const emailSchema = z.string().email();
  const isValidEmail = emailSchema.safeParse(email).success;

  // If invalid email, redirect to login page
  if (!isValidEmail) {
    redirect({ href: { pathname: "/login" }, locale });
  }

  return (
    <>
      <BackButton href="/login" />
      <div className="flex justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <OTPVerificationForm email={email as string} />
          </CardContent>
        </Card>
      </div>
    </>
  );
}
