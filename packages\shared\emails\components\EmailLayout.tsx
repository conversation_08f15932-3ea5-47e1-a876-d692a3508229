import * as React from "react";
import {
  Html,
  Body,
  Container,
  Section,
  Text,
  Img,
  Preview
} from "@react-email/components";
import { SupportedLocale } from "../../lib/i18n/types";
import { EmailTheme, getEmailThemeColors } from "../styles";

interface EmailLayoutProps {
  children: React.ReactNode;
  language?: SupportedLocale;
  theme?: EmailTheme;
  preview?: string;
}

export default function EmailLayout({
  children,
  language = "en",
  theme = "light",
  preview
}: EmailLayoutProps) {
  // Get colors based on theme
  const colors = getEmailThemeColors(theme);

  return (
    <Html lang={language}>
      {preview && <Preview>{preview}</Preview>}
      <Body
        style={{
          margin: "0 auto",
          padding: "40px 25px",
          backgroundColor: colors.bg,
          fontFamily: "Arial, sans-serif",
          fontWeight: "500"
        }}
      >
        <Container style={{ maxWidth: 375 }}>
          <Section>
            <Img
              src={
                (process.env.TEST_EMAIL_SUPABASE_URL ||
                  process.env.NEXT_PUBLIC_SUPABASE_URL ||
                  "http://127.0.0.1:54321") +
                `/storage/v1/object/public/asset/logo-${theme}`
              }
              alt="E-Senpai"
              style={{
                margin: "0px auto 40px",
                width: "275px",
                minHeight: theme === "light" ? "92px" : "80px"
              }}
            />

            {children}

            <Text
              style={{
                fontSize: "16px",
                color: colors.footer,
                marginTop: "48px",
                textAlign: "center" as const
              }}
            >
              {`© ${new Date().getFullYear()} E-Senpai`}
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}
