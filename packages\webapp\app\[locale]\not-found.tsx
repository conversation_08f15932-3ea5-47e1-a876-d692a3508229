"use client";

import { useTranslations } from "next-intl";
import { Link } from "shared/lib/i18n/navigation";
import { But<PERSON> } from "shared/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardT<PERSON>le,
  CardFooter
} from "shared/components/ui/card";
import { FolderX } from "lucide-react";

export default function NotFound() {
  const t = useTranslations("NotFound");

  return (
    <div className="flex w-full flex-col items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader className="pb-10">
          <CardTitle className="flex items-center gap-2 bg-lavender">
            <FolderX className="-ml-2 h-5 w-5" />
            {t("title")}
          </CardTitle>
        </CardHeader>

        <CardContent className="flex flex-col">
          <p className="mb-2">{t("message")}</p>
          <p className="text-sm text-main-foreground/70">{t("suggestion")}</p>
        </CardContent>

        <CardFooter className="flex justify-center">
          <Link href="/">
            <Button variant="default">{t("homeButton")}</Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
