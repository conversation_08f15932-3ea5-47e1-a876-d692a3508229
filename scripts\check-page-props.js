/**
 * <PERSON><PERSON><PERSON> to check all Next.js page components for proper Promise typing of params and searchParams
 */

import { execSync } from "child_process";
import { globSync } from "glob";
import path from "path";
import { fileURLToPath } from "url";

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const rootDir = path.resolve(__dirname, "..");

// Find all page.tsx files
const pageFiles = globSync("packages/webapp/app/**/**/page.tsx", {
  cwd: rootDir,
  absolute: true
});

console.log(`Found ${pageFiles.length} page components to check...`);

let hasErrors = false;

// Check each file
for (const file of pageFiles) {
  try {
    const relativePath = path.relative(rootDir, file).replace(/\\/g, "/");
    console.log(`Checking ${relativePath}...`);
    
    execSync(`npx eslint "${relativePath}" --no-ignore --rule "e-senpai/next-page-props-promise: error"`, {
      cwd: rootDir,
      stdio: "inherit"
    });
  } catch (error) {
    hasErrors = true;
    // Error is already displayed by ESLint
  }
}

if (hasErrors) {
  console.error("\nFound issues with page component props typing.");
  console.error("Please ensure all params and searchParams props are typed as Promise<T>.");
  process.exit(1);
} else {
  console.log("\n✅ All page components have correct Promise typing for params and searchParams!");
}
