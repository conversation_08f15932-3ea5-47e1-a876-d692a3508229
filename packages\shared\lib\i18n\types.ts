import { routing } from "./routing";

/**
 * Array of supported locales
 */
export const SUPPORTED_LOCALES = routing.locales;

/**
 * Type for supported locales
 */
export type SupportedLocale = (typeof SUPPORTED_LOCALES)[number];

/**
 * Type guard to check if a string is a supported locale
 */
export function isSupportedLocale(locale: string): locale is SupportedLocale {
  return SUPPORTED_LOCALES.includes(locale as SupportedLocale);
}
