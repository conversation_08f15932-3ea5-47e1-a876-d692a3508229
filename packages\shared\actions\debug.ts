"use server";

import { createClient } from "../lib/supabase/service";
import { tryCatch } from "../lib";

/**
 * Generates an OTP for a test user
 * This should only be used in development mode
 */
export async function generateTestOTP() {
  // Only allow in development mode
  if (process.env.NODE_ENV !== "development") {
    console.error(
      "Test OTP generation attempted in non-development environment"
    );
    return { error: "Not allowed in production", success: false };
  }

  const supabase = createClient();
  const testEmail = "<EMAIL>";

  // Generate a magic link with OTP
  const [authError, authResult] = await tryCatch(
    supabase.auth.admin.generateLink({
      type: "magiclink",
      email: testEmail
    })
  );

  if (authError || authResult.error) {
    console.error("Error generating OTP:", authError || authResult.error);
    return {
      error: "Failed to generate OTP",
      success: false
    };
  }

  // Extract the OTP from the response
  const otp = authResult.data.properties.email_otp;

  return {
    success: true,
    email: testEmail,
    otp
  };
}
