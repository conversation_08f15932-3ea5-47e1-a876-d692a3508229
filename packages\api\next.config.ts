import type { NextConfig } from "next";
import { load } from "dotenv-mono";
load();

const requiredEnvVars = [
  "NEXT_PUBLIC_SUPABASE_URL",
  "NEXT_PUBLIC_SUPABASE_ANON_KEY",
  "SUPABASE_SERVICE_ROLE_KEY",
  "API_CALL_SECRET",
  "EXCHANGE_RATE_API_KEY"
];

requiredEnvVars.forEach((envVar) => {
  if (!process.env[envVar]) {
    console.log(`Environment variable ${envVar} is not defined.`);
    throw new Error();
  }
});

const nextConfig: NextConfig = {
  /* config options here */
};

export default nextConfig;
