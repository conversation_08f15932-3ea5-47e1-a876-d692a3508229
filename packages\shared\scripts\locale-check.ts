#!/usr/bin/env tsx

/**
 * Locale Validation Script
 *
 * This script checks for missing or extra fields between locale files.
 * It compares all locale files against each other and reports any differences.
 * It also identifies unused translation keys in the English locale file.
 *
 * Usage:
 *   pnpm check-locales - Check for missing/extra fields and unused keys
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { SUPPORTED_LOCALES } from "../lib/i18n/types";
import chalk from "chalk";

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to locale files
const LOCALE_DIR = path.resolve(__dirname, "../locale");
const ROOT_DIR = path.resolve(__dirname, "../../../");

// Type for nested object structure
type NestedObject = {
  [key: string]: string | NestedObject;
};

// Function to load all locale files
function loadLocaleFiles(): Record<string, NestedObject> {
  const locales: Record<string, NestedObject> = {};

  for (const locale of SUPPORTED_LOCALES) {
    const filePath = path.join(LOCALE_DIR, `${locale}.json`);
    try {
      const content = fs.readFileSync(filePath, "utf8");
      locales[locale] = JSON.parse(content);
    } catch (error) {
      console.error(`Error loading locale file ${locale}.json:`, error);
      process.exit(1);
    }
  }

  return locales;
}

// Function to get all keys from a nested object (with dot notation)
function getAllKeys(obj: NestedObject, prefix = ""): string[] {
  let keys: string[] = [];

  for (const key in obj) {
    const currentKey = prefix ? `${prefix}.${key}` : key;
    if (typeof obj[key] === "object" && obj[key] !== null) {
      // Recursively get keys from nested objects
      keys = [...keys, ...getAllKeys(obj[key] as NestedObject, currentKey)];
    } else {
      keys.push(currentKey);
    }
  }

  return keys;
}

// Function to check if a key exists in an object (using dot notation)
function hasKey(obj: NestedObject, key: string): boolean {
  const parts = key.split(".");
  let current: any = obj;

  for (const part of parts) {
    if (
      current === undefined ||
      current === null ||
      typeof current !== "object"
    ) {
      return false;
    }
    current = current[part];
  }

  return current !== undefined;
}

// Function to get value at a key path (using dot notation)
function getValue(obj: NestedObject, key: string): any {
  const parts = key.split(".");
  let current: any = obj;

  for (const part of parts) {
    if (
      current === undefined ||
      current === null ||
      typeof current !== "object"
    ) {
      return undefined;
    }
    current = current[part];
  }

  return current;
}

// Function to generate JSON patch for missing keys
function generateJsonPatch(
  _locale: string, // Unused but kept for API consistency
  missingKeys: string[],
  referenceLocale: string,
  locales: Record<string, NestedObject>
): string {
  const patch: Record<string, any> = {};

  for (const key of missingKeys) {
    const parts = key.split(".");
    let current = patch;

    // Build the nested structure
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (!current[part]) {
        current[part] = {};
      }
      current = current[part];
    }

    // Set the value
    const lastPart = parts[parts.length - 1];
    current[lastPart] = getValue(locales[referenceLocale], key);
  }

  return JSON.stringify(patch, null, 2);
}

// Function to recursively find all files with specific extensions
function findFiles(
  dir: string,
  extensions: string[],
  excludeDirs: string[] = []
): string[] {
  const files: string[] = [];

  try {
    const entries = fs.readdirSync(dir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);

      // Skip excluded directories
      if (entry.isDirectory()) {
        if (!excludeDirs.includes(entry.name)) {
          files.push(...findFiles(fullPath, extensions, excludeDirs));
        }
      } else if (entry.isFile()) {
        const ext = path.extname(entry.name).toLowerCase();
        if (extensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error);
  }

  return files;
}

// Function to find all used translation keys and namespaces in the codebase
function findUsedTranslationsInfo(): {
  usedKeys: Set<string>;
  usedNamespaces: Set<string>;
} {
  const usedKeys: Set<string> = new Set();
  const usedNamespaces: Set<string> = new Set();

  // Define patterns to search for
  const patterns = [
    // Direct key access in t() function calls (both client and server components)
    { pattern: /t\(["|']([^"']+)["|']\)/g, type: "direct" },
    // Direct key access in useTranslations or getTranslations with namespace
    { pattern: /useTranslations\(["|']([^"']+)["|']\)/g, type: "namespace" },
    {
      pattern: /getTranslations\([^)]*namespace:\s*["|']([^"']+)["|']/g,
      type: "namespace"
    },
    // getTranslations with direct namespace parameter (server actions)
    { pattern: /getTranslations\(["|']([^"']+)["|']\)/g, type: "namespace" },
    // Await getTranslations pattern (server actions)
    {
      pattern: /await\s+getTranslations\(["|']([^"']+)["|']\)/g,
      type: "namespace"
    },
    // Direct access to translation objects (using dynamic pattern based on locale namespaces)
    {
      pattern: /\.([A-Z][A-Za-z0-9_]*)\b/g, // Match property access to PascalCase identifiers
      type: "section"
    },
    // Object destructuring pattern (e.g., const { key1, key2 } = translations)
    {
      pattern:
        /const\s*\{\s*([^{}]+)\s*\}\s*=\s*[^;]+(?:translations|t|locale)/g,
      type: "destructure"
    },
    // Object destructuring with different variable declaration
    {
      pattern: /let\s*\{\s*([^{}]+)\s*\}\s*=\s*[^;]+(?:translations|t|locale)/g,
      type: "destructure"
    },
    // Object destructuring with different variable declaration
    {
      pattern: /var\s*\{\s*([^{}]+)\s*\}\s*=\s*[^;]+(?:translations|t|locale)/g,
      type: "destructure"
    },
    // Direct import of namespace from translations
    {
      pattern: /import\s+\{\s*([^{}]+)\s*\}\s+from\s+['"]\S*\/locale['"]/g,
      type: "namespace"
    },
    // Direct reference to namespace in code
    {
      pattern: /(?:translations|messages)\.([A-Za-z0-9_]+)\b/g,
      type: "namespace"
    },

    // Direct usage of translation key in error messages
    { pattern: /err\([^,]+,\s*t\(["|']([^"']+)["|']\)/g, type: "direct" }
  ];

  try {
    // Define source directories to scan
    const sourceDirectories = [
      path.join(ROOT_DIR, "packages/webapp/app"),
      path.join(ROOT_DIR, "packages/shared")
    ];

    // Find all relevant files
    const fileExtensions = [".ts", ".tsx", ".js", ".jsx"];
    const excludeDirs = ["locale", "node_modules", ".next"]; // Exclude locale folder and others
    const allFiles: string[] = [];

    for (const dir of sourceDirectories) {
      if (fs.existsSync(dir)) {
        allFiles.push(...findFiles(dir, fileExtensions, excludeDirs));
      }
    }

    console.log(
      chalk.gray(`Scanning ${allFiles.length} files for translation keys...`)
    );

    // Process each file
    for (const filePath of allFiles) {
      try {
        const fileContent = fs.readFileSync(filePath, "utf8");

        // Check each pattern against the file content
        for (const { pattern, type } of patterns) {
          let match;
          const patternInstance = new RegExp(pattern.source, "g");

          while ((match = patternInstance.exec(fileContent)) !== null) {
            if (match[1]) {
              if (type === "namespace") {
                // This is a namespace
                usedNamespaces.add(match[1]);
              } else if (type === "section") {
                // This is a direct access to a section
                usedNamespaces.add(match[1]);
              } else if (type === "destructure") {
                // This is a destructuring pattern, handle multiple keys
                const keys = match[1].split(",").map((k) => k.trim());
                for (const key of keys) {
                  // Remove any default assignments (e.g., key = 'default')
                  const cleanKey = key.split("=")[0].trim();
                  usedKeys.add(cleanKey);
                }
              } else {
                // This is a direct key access
                usedKeys.add(match[1]);
              }
            }
          }
        }
      } catch (error) {
        console.error(`Error processing file ${filePath}:`, error);
      }
    }

    return { usedKeys, usedNamespaces };
  } catch (error) {
    console.error("Error finding used translation keys:", error);
    return { usedKeys: new Set(), usedNamespaces: new Set() };
  }
}

// Main function to check for missing or extra fields
function checkLocales() {
  console.log(
    chalk.blue("🔍 Checking locale files for missing or extra fields...")
  );

  // Load all locale files
  const locales = loadLocaleFiles();

  // Use English as the reference locale
  const referenceLocale = "en";
  const referenceKeys = getAllKeys(locales[referenceLocale]);

  let hasIssues = false;
  let totalMissingKeys = 0;
  let totalExtraKeys = 0;
  const localesWithIssues: string[] = [];

  // Check each locale against the reference locale
  for (const locale of SUPPORTED_LOCALES) {
    if (locale === referenceLocale) continue;

    const localeKeys = getAllKeys(locales[locale]);

    // Find missing keys (in reference but not in current locale)
    const missingKeys = referenceKeys.filter(
      (key) => !hasKey(locales[locale], key)
    );

    // Find extra keys (in current locale but not in reference)
    const extraKeys = localeKeys.filter(
      (key) => !hasKey(locales[referenceLocale], key)
    );

    // Report findings
    if (missingKeys.length > 0 || extraKeys.length > 0) {
      hasIssues = true;
      localesWithIssues.push(locale);
      totalMissingKeys += missingKeys.length;
      totalExtraKeys += extraKeys.length;

      console.log(chalk.yellow(`\n📄 ${locale}.json:`));

      if (missingKeys.length > 0) {
        console.log(chalk.red(`  ❌ Missing keys (${missingKeys.length}):`));
        missingKeys.forEach((key) => {
          const value = getValue(locales[referenceLocale], key);
          console.log(`    - ${key}: "${value}"`);
        });

        // Generate JSON patch
        console.log(
          chalk.blue(`\n  💡 JSON patch to add missing keys to ${locale}.json:`)
        );
        console.log(
          chalk.cyan(
            `  ${generateJsonPatch(locale, missingKeys, referenceLocale, locales)}`
          )
        );
      }

      if (extraKeys.length > 0) {
        console.log(chalk.magenta(`  ⚠️ Extra keys (${extraKeys.length}):`));
        extraKeys.forEach((key) => {
          const value = getValue(locales[locale], key);
          console.log(`    - ${key}: "${value}"`);
        });
      }
    }
  }

  // Summary
  console.log(chalk.blue("\n📊 Summary:"));
  if (!hasIssues) {
    console.log(
      chalk.green(
        "  ✅ All locale files are in sync! No missing or extra fields found."
      )
    );
  } else {
    console.log(
      chalk.yellow(
        `  ⚠️ Found issues in ${localesWithIssues.length} locale files: ${localesWithIssues.join(", ")}`
      )
    );
    console.log(chalk.red(`  ❌ Total missing keys: ${totalMissingKeys}`));
    console.log(chalk.magenta(`  ⚠️ Total extra keys: ${totalExtraKeys}`));
    console.log(
      chalk.yellow("\nPlease update the locale files to ensure consistency.")
    );
    console.log(
      chalk.blue(
        "You can use the JSON patches above to add missing keys to each locale file."
      )
    );
  }

  // Check for unused keys in the English locale
  checkUnusedKeys(locales[referenceLocale]);
}

// Function to check for unused keys and namespaces in the English locale
function checkUnusedKeys(enLocale: NestedObject) {
  console.log(
    chalk.blue("\n🔍 Checking for unused translation keys in English locale...")
  );

  // Get all keys from the English locale
  const allKeys = getAllKeys(enLocale);

  // Find all directly used keys and namespaces in the codebase
  const { usedKeys: directlyUsedKeys, usedNamespaces } =
    findUsedTranslationsInfo();

  console.log(
    chalk.gray(
      `Found ${directlyUsedKeys.size} directly used translation keys and ${usedNamespaces.size} namespaces in the codebase.`
    )
  );

  // Create a map of namespace to keys
  const keysByNamespace: Record<string, string[]> = {};

  // Get all top-level namespaces from the locale file
  const namespaces = Object.keys(enLocale);

  // Group keys by namespace
  allKeys.forEach((key) => {
    const parts = key.split(".");
    const namespace = parts[0];

    if (!keysByNamespace[namespace]) {
      keysByNamespace[namespace] = [];
    }

    keysByNamespace[namespace].push(key);
  });

  // Find unused keys by namespace
  const unusedKeys: string[] = [];

  // Find unused namespaces
  const unusedNamespaces: string[] = [];

  // Check each namespace to see if it's used
  namespaces.forEach((namespace) => {
    if (!usedNamespaces.has(namespace)) {
      unusedNamespaces.push(namespace);
    }
  });

  // Create a set of all child keys that might be used directly without namespace
  const allChildKeys = new Set<string>();
  allKeys.forEach((key) => {
    const parts = key.split(".");
    if (parts.length > 1) {
      // Add the last part as a potential direct key
      allChildKeys.add(parts[parts.length - 1]);
    }
  });

  // Check which child keys are actually used directly
  const usedChildKeys = new Set<string>();
  directlyUsedKeys.forEach((key) => {
    if (!key.includes(".") && allChildKeys.has(key)) {
      usedChildKeys.add(key);
    }
  });

  console.log(
    chalk.gray(
      `Found ${usedChildKeys.size} child keys that might be used directly without namespace.`
    )
  );

  // Check each key in each namespace
  Object.entries(keysByNamespace).forEach(([namespace, keys]) => {
    // Skip checking keys in unused namespaces (they'll all be unused)
    if (unusedNamespaces.includes(namespace)) {
      return;
    }

    // For each key in this namespace
    keys.forEach((key) => {
      // Get the key without the namespace prefix (for t() function calls)
      const keyWithoutNamespace = key.substring(key.indexOf(".") + 1);

      // Get just the last part of the key (for direct usage without namespace)
      const parts = key.split(".");
      const lastPart = parts[parts.length - 1];

      // Check if the key is used directly in any form
      if (
        !directlyUsedKeys.has(keyWithoutNamespace) &&
        !directlyUsedKeys.has(key) &&
        !usedChildKeys.has(lastPart)
      ) {
        unusedKeys.push(key);
      }
    });
  });

  // Report findings for unused keys
  if (unusedKeys.length > 0) {
    console.log(
      chalk.yellow(
        `\n🔍 Found ${unusedKeys.length} potentially unused keys in English locale:`
      )
    );
    unusedKeys.forEach((key) => {
      const value = getValue(enLocale, key);
      console.log(chalk.gray(`  - ${key}: "${value}"`));
    });
  } else {
    console.log(
      chalk.green("  ✅ No unused keys found in the English locale!")
    );
  }

  // Report findings for unused namespaces
  if (unusedNamespaces.length > 0) {
    console.log(
      chalk.yellow(
        `\n🔍 Found ${unusedNamespaces.length} potentially unused namespaces in English locale:`
      )
    );
    unusedNamespaces.forEach((namespace) => {
      console.log(chalk.red(`  - ${namespace}`));

      // Show all keys in this namespace
      const keysInNamespace = keysByNamespace[namespace] || [];
      if (keysInNamespace.length > 0) {
        console.log(chalk.gray(`    Contains ${keysInNamespace.length} keys:`));
        keysInNamespace.forEach((key) => {
          const value = getValue(enLocale, key);
          console.log(chalk.gray(`      - ${key}: "${value}"`));
        });
      }
    });
  } else {
    console.log(
      chalk.green("  ✅ No unused namespaces found in the English locale!")
    );
  }

  // Add a note about dynamic usage
  if (unusedKeys.length > 0 || unusedNamespaces.length > 0) {
    console.log(
      chalk.yellow(
        "\nNote: These keys and namespaces might still be used dynamically or in ways not detected by this script."
      )
    );
  }
}

// Run the script
checkLocales();
