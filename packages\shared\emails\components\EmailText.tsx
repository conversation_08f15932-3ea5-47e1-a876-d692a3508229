import * as React from "react";
import { Text } from "@react-email/components";

interface EmailTextProps {
  children: React.ReactNode;
  color: string;
  padding?: string;
  marginBottom?: string;
}

export default function EmailText({
  children,
  color,
  padding = "4px",
  marginBottom = "12px"
}: EmailTextProps) {
  // Fixed font size and line height
  const fontSize = "18px";
  const lineHeight = "26px";

  return (
    <Text
      style={{
        fontSize,
        lineHeight,
        color,
        padding,
        marginBottom
      }}
    >
      {children}
    </Text>
  );
}
