{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_42788bc2._.js", "server/edge/chunks/41428_@formatjs_intl-localematcher_lib_d637b060._.js", "server/edge/chunks/4294a_@supabase_auth-js_dist_module_51b8369e._.js", "server/edge/chunks/node_modules__pnpm_0c0ce5b1._.js", "server/edge/chunks/[root-of-the-server]__569f5a63._.js", "server/edge/chunks/packages_webapp_edge-wrapper_1a52fb86.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|ingest|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|ingest|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oKH2XG+pncrIx4/k4x1NQVrid0DXNmC2fOUTyxzd+Sk=", "__NEXT_PREVIEW_MODE_ID": "5c258c6cefe208b37b542a826c5de6f9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a2ff6db2154b8ebd05313b3910386a5260fa5f77d62daa82d1e200fa9e094786", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c0054fddb42851bcfbf6ef63b92556351e8b2e2c76c6e044d8286f1b8213c98f"}}}, "instrumentation": null, "functions": {}}