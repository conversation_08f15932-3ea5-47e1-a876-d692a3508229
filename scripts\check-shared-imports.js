/**
 * <PERSON><PERSON>t to check for shared/ prefix imports within the shared package
 */

import { execSync } from "child_process";
import { globSync } from "glob";
import path from "path";
import { fileURLToPath } from "url";

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const rootDir = path.resolve(__dirname, "..");
const sharedDir = path.resolve(rootDir, "packages/shared");

// Find all TypeScript and JavaScript files in the shared package
const sharedFiles = globSync("**/*.{ts,tsx,js,jsx}", {
  cwd: sharedDir,
  absolute: true,
  ignore: ["**/node_modules/**", "**/.next/**", "**/dist/**"]
});

console.log(`Found ${sharedFiles.length} files to check in the shared package...`);

let hasErrors = false;

// Check each file
for (const file of sharedFiles) {
  try {
    const relativePath = path.relative(rootDir, file).replace(/\\/g, "/");
    console.log(`Checking ${relativePath}...`);
    
    execSync(`npx eslint "${relativePath}" --no-ignore --rule "e-senpai/no-shared-prefix-imports: error"`, {
      cwd: rootDir,
      stdio: "inherit"
    });
  } catch (error) {
    hasErrors = true;
    // Error is already displayed by ESLint
  }
}

if (hasErrors) {
  console.error("\nFound issues with shared/ prefix imports in the shared package.");
  console.error("Please use relative imports instead of 'shared/' prefix within the shared package.");
  process.exit(1);
} else {
  console.log("\n✅ All imports in the shared package are using relative paths correctly!");
}
