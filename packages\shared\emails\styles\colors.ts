/**
 * Email theme colors for light and dark themes
 */

export type EmailTheme = "light" | "dark";

export interface EmailThemeColors {
  main: string;
  border: string;
  text: string;
  bg: string;
  hr: string;
  footer: string;
  otpText: string;
}

export type EmailThemeColorSet = {
  [key in EmailTheme]: EmailThemeColors;
};

/**
 * Centralized email theme colors for consistent styling across all email templates
 */
export const emailThemeColors: EmailThemeColorSet = {
  light: {
    main: "#ffdc58", // Yellow
    border: "#2c1c44", // Dark purple
    text: "#2c1c44", // Dark purple
    bg: "#fef2e8", // Light beige
    hr: "#dddddd", // Light gray
    footer: "#666666", // Medium gray
    otpText: "#2c1c44" // Same as text for light theme
  },
  dark: {
    main: "#ffdc58", // Yellow
    border: "#2c1c44", // Dark purple
    text: "#d3d3d3", // Light gray
    bg: "#1a1a2e", // Dark blue
    hr: "#444444", // Dark gray
    footer: "#aaaaaa", // Light gray
    otpText: "#000000" // Black for OTP text in dark theme
  }
};

/**
 * Get theme colors based on the selected theme
 * @param theme The email theme (light or dark)
 * @returns The colors for the selected theme
 */
export function getEmailThemeColors(theme: EmailTheme): EmailThemeColors {
  return emailThemeColors[theme];
}
