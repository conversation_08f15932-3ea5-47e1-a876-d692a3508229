import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Tit<PERSON>
} from "shared/components/ui/card";

type SectionCardProps = {
  id: string;
  title: string;
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  delay?: number;
};

export function SectionCard({
  id,
  title,
  children,
  className,
  style,
  delay
}: SectionCardProps) {
  return (
    <Card id={id} className={className} style={style} delay={delay}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
}
