"use client";

import * as React from "react";
import { AlertCircle } from "lucide-react";
import { Input } from "../ui/input";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger
} from "../ui/hover-card";
import { cn } from "../../lib";

export interface InputWithErrorProps extends React.ComponentProps<"input"> {
  errorMessage?: string;
  side?: "top" | "right" | "bottom" | "left";
  sideOffset?: number;
}

function InputWithError({
  className,
  errorMessage = "",
  side = "top",
  sideOffset = 4,
  ...props
}: InputWithErrorProps) {
  const hasError = !!errorMessage;

  return (
    <div className="relative w-full">
      <Input
        className={cn(
          hasError && "border-black focus-visible:ring-red-500",
          className
        )}
        {...props}
      />
      {hasError && (
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <HoverCard>
            <HoverCardTrigger asChild>
              <div className="pointer-events-auto">
                <AlertCircle className="h-6 w-6 stroke-red-500" />
              </div>
            </HoverCardTrigger>
            <HoverCardContent
              side={side}
              sideOffset={sideOffset}
              className="w-52"
            >
              {errorMessage}
            </HoverCardContent>
          </HoverCard>
        </div>
      )}
    </div>
  );
}

InputWithError.displayName = "InputWithError";

export { InputWithError };
