// Import email translations from locale files
import enLocale from "../../locale/en.json";
import koLocale from "../../locale/ko.json";
import jaLocale from "../../locale/ja.json";
import trLocale from "../../locale/tr.json";
import { SupportedLocale } from "../i18n/types";

// Create a map of email translations by language
export const emailTranslations = {
  en: enLocale.Email,
  ko: koLocale.Email,
  ja: jaLocale.Email,
  tr: trLocale.Email
} satisfies { [key in SupportedLocale]: typeof enLocale.Email };

/**
 * Get email translations for a specific language
 *
 * @param language - Language code (en, ko, ja, tr)
 * @returns Email translations for the specified language, or English as fallback
 */
export function getEmailTranslations(language: SupportedLocale) {
  return emailTranslations[language] || emailTranslations.en;
}
