/**
 * Account Realtime Presence Test - Online Presence
 *
 * This test demonstrates tracking authenticated users' online status
 * using Supabase Realtime Presence API with multiple clients.
 *
 * Key concepts tested:
 * - Multiple Supabase clients connecting to presence channel simultaneously
 * - Tracking user online/offline state with actual presence.track()
 * - Real-time presence sync between clients
 * - User join/leave events
 *
 * Use cases:
 * - Show "online now" indicators on user profiles
 * - Display active user counts
 * - Track who's currently online across the website
 *
 * Sources:
 * - https://supabase.com/docs/guides/realtime/presence?queryGroups=language&language=js
 * - https://supabase.com/docs/guides/realtime/authorization?queryGroups=language&language=js
 */

import { beforeAll, describe, expect, test } from "vitest";
import { mockCustomer, mockProvider, MockUser } from "./mocks/auth.user";
import { RealtimeChannel } from "@supabase/supabase-js";
import { createRealtimeChannel } from "shared/lib/supabase/realtime";

type OnlinePresenceState = {
  user_id: string;
  online: boolean;
};

async function createOnlinePresenceChannel(
  user: MockU<PERSON>
): Promise<RealtimeChannel> {
  if (!user.client) throw new Error("User client is undefined");
  if (!user.data) throw new Error("User data is undefined");

  const channelName = "online:global";
  const channelConfig = {
    config: { private: false }
  };

  // Create channel with integrated health check
  const channel = await createRealtimeChannel({
    client: user.client,
    channelName,
    channelConfig,
    healthCheck: {
      healthCheckFn: async (channel) => {
        // Perform a simple health check - track presence
        await channel.track({
          user_id: user.data!.id,
          online: true
        });
      },
      validateFn: (channel) => {
        // Validate that presence state was established
        const state = channel.presenceState();
        return Object.keys(state).length > 0;
      },
      options: {
        maxAttempts: 10,
        checkDelay: 500,
        totalTimeout: 15000
      }
    },
    connectionOptions: {
      maxRetries: 3,
      baseDelay: 2000,
      maxDelay: 8000,
      connectionTimeout: 15000
    }
  });

  return channel;
}

function getOnlineUserIds(channel: RealtimeChannel): string[] {
  const presenceState = channel.presenceState<OnlinePresenceState>();
  const userIds: string[] = [];

  Object.values(presenceState).forEach((presences) => {
    presences.forEach((presence) => {
      if (presence.user_id && presence.online) {
        userIds.push(presence.user_id);
      }
    });
  });

  return userIds;
}

const customer = mockCustomer();
const provider = mockProvider();

describe("online presence", () => {
  let customerChannel: RealtimeChannel;
  let providerChannel: RealtimeChannel;

  beforeAll(async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    // Create online presence channels for both users with robust connection
    customerChannel = await createOnlinePresenceChannel(customer);
    providerChannel = await createOnlinePresenceChannel(provider);

    console.log("Both presence channels connected successfully");
  }, 60000);

  test("can connect multiple clients to presence channel", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    // Verify channels were created successfully
    expect(customerChannel).toBeDefined();
    expect(providerChannel).toBeDefined();

    // Both users track their online presence
    const customerTrackResult = await customerChannel.track({
      user_id: customer.data.id,
      online: true
    });

    const providerTrackResult = await providerChannel.track({
      user_id: provider.data.id,
      online: true
    });

    // Track calls should succeed or at least not throw
    expect(customerTrackResult).toBeDefined();
    expect(providerTrackResult).toBeDefined();

    // Wait for presence sync
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Check that both users can see each other's presence
    const customerOnlineUsers = getOnlineUserIds(customerChannel);
    const providerOnlineUsers = getOnlineUserIds(providerChannel);

    console.log("Customer sees online users:", customerOnlineUsers);
    console.log("Provider sees online users:", providerOnlineUsers);

    // Both users should see themselves and potentially each other
    expect(customerOnlineUsers).toContain(customer.data.id);
    expect(providerOnlineUsers).toContain(provider.data.id);
  });

  test("can track online status with presence API", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");

    // Track user as online
    const trackResult = await customerChannel.track({
      user_id: customer.data.id,
      online: true
    });

    // Track call should succeed or at least not throw
    expect(trackResult).toBeDefined();

    // Wait for potential presence sync
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Check that user appears in online users list
    const onlineUsers = getOnlineUserIds(customerChannel);
    console.log("Online users after tracking:", onlineUsers);

    // Customer should see themselves as online
    expect(onlineUsers).toContain(customer.data.id);
  });

  test("can handle user going offline", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");

    // First, track user as online
    await customerChannel.track({
      user_id: customer.data.id,
      online: true
    });

    await new Promise((resolve) => setTimeout(resolve, 500));

    // Verify user is online
    let onlineUsers = getOnlineUserIds(customerChannel);
    expect(onlineUsers).toContain(customer.data.id);
    console.log("Users online before going offline:", onlineUsers);

    // User goes offline by untracking
    await customerChannel.untrack();

    // Wait for presence sync
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Check that user is no longer in online users list
    onlineUsers = getOnlineUserIds(customerChannel);
    console.log("Users online after going offline:", onlineUsers);
    expect(onlineUsers).not.toContain(customer.data.id);

    // User comes back online
    const trackResult = await customerChannel.track({
      user_id: customer.data.id,
      online: true
    });

    expect(trackResult).toBeDefined();

    await new Promise((resolve) => setTimeout(resolve, 500));

    // Verify user is back online
    onlineUsers = getOnlineUserIds(customerChannel);
    console.log("Users online after coming back:", onlineUsers);
    expect(onlineUsers).toContain(customer.data.id);
  });

  test("can handle multiple users online simultaneously", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    // Both users track their online presence
    const customerTrackResult = await customerChannel.track({
      user_id: customer.data.id,
      online: true
    });

    const providerTrackResult = await providerChannel.track({
      user_id: provider.data.id,
      online: true
    });

    expect(customerTrackResult).toBeDefined();
    expect(providerTrackResult).toBeDefined();

    // Wait for presence sync
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Check that both users can see each other online
    const customerOnlineUsers = getOnlineUserIds(customerChannel);
    const providerOnlineUsers = getOnlineUserIds(providerChannel);

    console.log("Customer sees online users:", customerOnlineUsers);
    console.log("Provider sees online users:", providerOnlineUsers);

    // Each user should see themselves online
    expect(customerOnlineUsers).toContain(customer.data.id);
    expect(providerOnlineUsers).toContain(provider.data.id);

    // If they're on the same global channel, they should see each other
    // (This depends on the channel configuration)
    const totalUniqueUsers = new Set([
      ...customerOnlineUsers,
      ...providerOnlineUsers
    ]);
    console.log("Total unique online users:", Array.from(totalUniqueUsers));

    // At minimum, we should have both users tracked somewhere
    expect(totalUniqueUsers.has(customer.data.id)).toBe(true);
    expect(totalUniqueUsers.has(provider.data.id)).toBe(true);
  });

  test("can verify user presence state changes", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");

    // First ensure user is offline
    await customerChannel.untrack();
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Check initial state
    let onlineUsers = getOnlineUserIds(customerChannel);
    const wasOnlineInitially = onlineUsers.includes(customer.data.id);
    console.log("Initial online users:", onlineUsers);
    console.log("Customer initially online:", wasOnlineInitially);

    // Track user as online
    await customerChannel.track({
      user_id: customer.data.id,
      online: true
    });

    await new Promise((resolve) => setTimeout(resolve, 500));

    // Verify user appears in presence
    onlineUsers = getOnlineUserIds(customerChannel);
    expect(onlineUsers).toContain(customer.data.id);
    console.log("Online users after tracking:", onlineUsers);

    // Go offline
    await customerChannel.untrack();

    await new Promise((resolve) => setTimeout(resolve, 500));

    // Verify user disappears from presence
    onlineUsers = getOnlineUserIds(customerChannel);
    expect(onlineUsers).not.toContain(customer.data.id);

    console.log("Final online users after going offline:", onlineUsers);
  });
});
