import type { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";
import { load } from "dotenv-mono";
load();

const requiredEnvVars = [
  "NEXT_PUBLIC_SUPABASE_URL",
  "NEXT_PUBLIC_SUPABASE_ANON_KEY",
  "SUPABASE_SERVICE_ROLE_KEY",
  "NEXT_PUBLIC_HCAPTCHA_SITE_KEY",
  "NEXT_PUBLIC_POSTHOG_KEY",
  "NEXT_PUBLIC_POSTHOG_HOST"
];

requiredEnvVars.forEach((envVar) => {
  if (!process.env[envVar]) {
    console.log(`Environment variable ${envVar} is not defined.`);
    throw new Error();
  }
});

const nextConfig: NextConfig = {
  async rewrites() {
    return [
      {
        source: "/ingest/static/:path*",
        destination: "https://eu-assets.i.posthog.com/static/:path*"
      },
      {
        source: "/ingest/:path*",
        destination: "https://eu.i.posthog.com/:path*"
      },
      {
        source: "/ingest/decide",
        destination: "https://eu.i.posthog.com/decide"
      }
    ];
  },
  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,
  images: {
    // Cache the images for 1 month (31 days)
    minimumCacheTTL: 2678400,
    formats: ["image/webp"],
    qualities: [75],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "*.ufs.sh"
      }
      // {
      //   protocol: "https",
      //   hostname: "drive.google.com",
      //   pathname: "/uc"
      // }
    ]
  }
};

const withNextIntl = createNextIntlPlugin("../shared/lib/i18n/request.ts");

export default withNextIntl(nextConfig);
