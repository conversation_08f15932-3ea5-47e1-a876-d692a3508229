"use client";

import * as React from "react";
import { motion } from "motion/react";
import { useTransition } from "../components/provider/transition-provider";

type MotionWrapperProps = React.ComponentPropsWithoutRef<typeof motion.div> & {
  delay?: number;
};

function MotionWrapper({ children, delay }: MotionWrapperProps) {
  const { isTransitioning } = useTransition();

  const entering = { scale: 1, opacity: 1 };
  const leaving = { scale: 0, opacity: 0 };

  return (
    <motion.div
      initial={leaving}
      animate={isTransitioning ? leaving : entering}
      transition={{ delay: isTransitioning ? 0 : (delay ?? 0) }}
    >
      {children}
    </motion.div>
  );
}

export { MotionWrapper };
