"use client";

import { OTPInput, OTPInputContext } from "input-otp";
import { Dot } from "lucide-react";
import * as React from "react";
import { cn } from "../../lib";

function InputOTP({
  className,
  containerClassName,
  onChange,
  ...props
}: React.ComponentProps<typeof OTPInput> & {
  containerClassName?: string;
}) {
  // Handle change to filter out non-numeric characters
  const handleChange = (value: string) => {
    // Only allow numeric characters (0-9)
    const numericValue = value.replace(/[^0-9]/g, "");

    // Call the original onChange handler with the filtered value
    if (onChange) {
      onChange(numericValue);
    }
  };

  // Handle keydown to prevent non-numeric input
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Allow only numeric keys, backspace, delete, tab, arrow keys, and enter
    const allowedKeys = [
      "Backspace",
      "Delete",
      "Tab",
      "Enter",
      "<PERSON>Lef<PERSON>",
      "<PERSON>R<PERSON>",
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9"
    ];

    // Also allow numeric keypad keys
    const numericKeypadKeys = [
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9"
    ].map((num) => `Numpad${num}`);

    // Check if the pressed key is allowed
    if (
      !allowedKeys.includes(e.key) &&
      !numericKeypadKeys.includes(e.code) &&
      !e.ctrlKey && // Allow Ctrl+C, Ctrl+V, etc.
      !e.metaKey // Allow Cmd+C, Cmd+V, etc. on Mac
    ) {
      e.preventDefault();
    }
  };

  return (
    <OTPInput
      data-slot="input-otp"
      containerClassName={cn(
        "flex items-center gap-2 has-disabled:opacity-50",
        containerClassName
      )}
      className={cn("disabled:cursor-not-allowed", className)}
      onChange={handleChange}
      pattern="[0-9]*"
      inputMode="numeric"
      {...props}
    />
  );
}

function InputOTPGroup({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="input-otp-group"
      className={cn("flex items-center", className)}
      {...props}
    />
  );
}

function InputOTPSlot({
  index,
  className,
  ...props
}: React.ComponentProps<"div"> & { index: number }) {
  const inputOTPContext = React.useContext(OTPInputContext);
  const { char, hasFakeCaret, isActive } = inputOTPContext?.slots[index] ?? {};

  return (
    <div
      data-slot="input-otp-slot"
      data-active={isActive}
      className={cn(
        "relative flex size-10 items-center justify-center border-y-2 border-r-2 border-border bg-secondary-background text-xl font-semibold text-foreground first:rounded-l-base first:border-l-2 last:rounded-r-base transition-all",
        isActive && "z-10 ring-1 ring-ring",
        className
      )}
      {...props}
    >
      {char}
      {hasFakeCaret && (
        <div className="pointer-events-none absolute inset-0 flex items-center justify-center">
          <div className="h-4 w-px animate-caret-blink bg-current duration-1000" />
        </div>
      )}
    </div>
  );
}

function InputOTPSeparator({ ...props }: React.ComponentProps<"div">) {
  return (
    <div data-slot="input-otp-separator" role="separator" {...props}>
      <Dot className="size-4" />
    </div>
  );
}

export { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator };
